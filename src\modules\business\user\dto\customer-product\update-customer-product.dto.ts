import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  MaxLength,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductTypeEnum, PriceTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';

/**
 * DTO cho việc cập nhật sản phẩm khách hàng
 */
export class UpdateCustomerProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp - Phiên bản mới',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  name?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết sản phẩm',
    example: '<PERSON><PERSON> thun nam chất liệu cotton 100%, thiết kế hiện đại với logo mới',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm (JSONB)',
    example: {
      listPrice: 550000,
      salePrice: 500000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: any;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['thời trang', 'nam', 'cotton', 'premium'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityStatusEnum)
  status?: EntityStatusEnum;

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}
