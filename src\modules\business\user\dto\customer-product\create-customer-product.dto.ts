import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';

/**
 * DTO cho việc tạo sản phẩm khách hàng mới
 */
export class CreateCustomerProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: '<PERSON>o thun nam cao cấp',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết sản phẩm',
    example: '<PERSON>o thun nam chất liệu cotton 100%, thiết kế hiện đại',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm (JSONB)',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  price?: any;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['thời trang', 'nam', 'cotton'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}
