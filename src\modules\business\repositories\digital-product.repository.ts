import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { DigitalProduct } from '../entities/digital-product.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho DigitalProduct entity
 * Xử lý các thao tác database cho sản phẩm số
 */
@Injectable()
export class DigitalProductRepository {
  private readonly logger = new Logger(DigitalProductRepository.name);

  constructor(
    @InjectRepository(DigitalProduct)
    private readonly repository: Repository<DigitalProduct>,
  ) {}

  /**
   * Tạo sản phẩm số mới
   * @param data Dữ liệu sản phẩm số
   * @returns Sản phẩm số đã tạo
   */
  async create(data: Partial<DigitalProduct>): Promise<DigitalProduct> {
    const digitalProduct = this.repository.create(data);
    return this.repository.save(digitalProduct);
  }

  /**
   * <PERSON><PERSON><PERSON> sản phẩm số theo ID
   * @param id ID sản phẩm số
   * @returns Sản phẩm số hoặc null
   */
  async findById(id: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm sản phẩm số theo productId
   * @param productId ID sản phẩm chính
   * @returns Sản phẩm số hoặc null
   */
  async findByProductId(productId: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { productId },
    });
  }

  /**
   * Lấy danh sách sản phẩm số với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm số với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    productId?: number;
    deliveryMethod?: string;
    timingType?: string;
  }): Promise<PaginatedResult<DigitalProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      productId,
      deliveryMethod,
      timingType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('digitalProduct');

    // Filter theo productId nếu có
    if (productId) {
      queryBuilder.andWhere('digitalProduct.productId = :productId', { productId });
    }

    // Filter theo deliveryMethod nếu có
    if (deliveryMethod) {
      queryBuilder.andWhere('digitalProduct.deliveryMethod = :deliveryMethod', { deliveryMethod });
    }

    // Filter theo timingType nếu có
    if (timingType) {
      queryBuilder.andWhere('digitalProduct.timingType = :timingType', { timingType });
    }

    // Tìm kiếm theo tên sản phẩm nếu có
    if (search) {
      queryBuilder.leftJoin('digitalProduct.product', 'product');
      queryBuilder.andWhere('product.name ILIKE :search', { 
        search: `%${search}%` 
      });
    }

    // Sắp xếp theo ID mới nhất
    queryBuilder.orderBy('digitalProduct.id', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Cập nhật sản phẩm số
   * @param id ID sản phẩm số
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm số đã cập nhật
   */
  async update(id: number, data: Partial<DigitalProduct>): Promise<DigitalProduct> {
    await this.repository.update(id, data);

    const updatedProduct = await this.findById(id);
    if (!updatedProduct) {
      throw new Error(`DigitalProduct with ID ${id} not found after update`);
    }

    return updatedProduct;
  }

  /**
   * Xóa sản phẩm số
   * @param id ID sản phẩm số
   */
  async delete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Xóa sản phẩm số theo productId
   * @param productId ID sản phẩm chính
   */
  async deleteByProductId(productId: number): Promise<void> {
    await this.repository.delete({ productId });
  }

src/modules/business/repositories/digital-product.repository.ts:48:16 - error TS2353: Object literal may only specify known properties, and 'productId' does not exist in type 'FindOptionsWhere<DigitalProduct> | FindOptionsWhere<DigitalProduct>[]'.

48       where: { productId },
                  ~~~~~~~~~

  node_modules/typeorm/find-options/FindOneOptions.d.ts:23:5
    23     where?: FindOptionsWhere<Entity>[] | FindOptionsWhere<Entity>;
           ~~~~~
    The expected type comes from property 'where' which is declared here on type 'FindOneOptions<DigitalProduct>'

src/modules/business/repositories/digital-product.repository.ts:112:9 - error TS2353: Object literal may only specify known properties, and 'total' does not exist in type 'PaginationMeta'.

112         total,
            ~~~~~

  src/common/response/api-response-dto.ts:287:3        
    287   meta: PaginationMeta;
          ~~~~
    The expected type comes from property 'meta' which is declared here on type 'PaginatedResult<DigitalProduct>'

src/modules/business/repositories/digital-product.repository.ts:150:36 - error TS2353: Object literal may only specify known properties, and 'productId' does not exist in type 'FindOptionsWhere<DigitalProduct> | Date | ObjectId | string[] | number[] | Date[] | ObjectId[]'.  

150     await this.repository.delete({ productId });   
                                       ~~~~~~~~~       

src/modules/business/repositories/digital-product.repository.ts:182:16 - error TS2353: Object literal may only specify known properties, and 'productId' does not exist in type 'FindOptionsWhere<DigitalProduct> | FindOptionsWhere<DigitalProduct>[]'.

182       where: { productId },
                   ~~~~~~~~~

src/modules/business/repositories/digital-product.repository.ts:204:16 - error TS2353: Object literal may only specify known properties, and 'timingType' does not exist in type 'FindOptionsWhere<DigitalProduct> | FindOptionsWhere<DigitalProduct>[]'.

204       where: { timingType },
                   ~~~~~~~~~~

src/modules/business/repositories/digital-product.repository.ts:215:16 - error TS2353: Object literal may only specify known properties, and 'productId' does not exist in type 'FindOptionsWhere<DigitalProduct> | FindOptionsWhere<DigitalProduct>[]'.

215       where: { productId },
                   ~~~~~~~~~

src/modules/business/repositories/digital-product.repository.ts:285:9 - error TS2353: Object literal may only specify known properties, and 'total' does not exist in type 'PaginationMeta'.

285         total,
            ~~~~~

  src/common/response/api-response-dto.ts:287:3        
    287   meta: PaginationMeta;
          ~~~~
    The expected type comes from property 'meta' which is declared here on type 'PaginatedResult<DigitalProduct>'

src/modules/business/repositories/entity-has-media.repository.ts:173:9 - error TS2353: Object literal may only specify known properties, and 'total' does not e
  /**
   * Lưu sản phẩm số
   * @param digitalProduct Sản phẩm số cần lưu
   * @returns Sản phẩm số đã lưu
   */
  async save(digitalProduct: DigitalProduct): Promise<DigitalProduct> {
    return this.repository.save(digitalProduct);
  }

  /**
   * Tìm sản phẩm số theo danh sách IDs
   * @param ids Danh sách ID
   * @returns Danh sách sản phẩm số
   */
  async findByIds(ids: number[]): Promise<DigitalProduct[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Đếm số lượng sản phẩm số theo productId
   * @param productId ID sản phẩm chính
   * @returns Số lượng sản phẩm số
   */
  async countByProductId(productId: number): Promise<number> {
    return this.repository.count({
      where: { productId },
    });
  }

  /**
   * Tìm sản phẩm số theo deliveryMethod
   * @param deliveryMethod Phương thức giao hàng
   * @returns Danh sách sản phẩm số
   */
  async findByDeliveryMethod(deliveryMethod: string): Promise<DigitalProduct[]> {
    return this.repository.find({
      where: { deliveryMethod },
    });
  }

  /**
   * Tìm sản phẩm số theo timingType
   * @param timingType Loại thời gian
   * @returns Danh sách sản phẩm số
   */
  async findByTimingType(timingType: string): Promise<DigitalProduct[]> {
    return this.repository.find({
      where: { timingType },
    });
  }

  /**
   * Kiểm tra sản phẩm số có tồn tại theo productId
   * @param productId ID sản phẩm chính
   * @returns true nếu tồn tại, false nếu không
   */
  async existsByProductId(productId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { productId },
    });
    return count > 0;
  }

  /**
   * Lấy sản phẩm số với thông tin sản phẩm chính
   * @param id ID sản phẩm số
   * @returns Sản phẩm số với thông tin sản phẩm chính
   */
  async findByIdWithProduct(id: number): Promise<DigitalProduct | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['product'],
    });
  }

  /**
   * Lấy danh sách sản phẩm số với thông tin sản phẩm chính
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm số với thông tin sản phẩm chính
   */
  async findAllWithProduct(query: {
    page?: number;
    limit?: number;
    search?: string;
    deliveryMethod?: string;
    timingType?: string;
  }): Promise<PaginatedResult<DigitalProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      deliveryMethod,
      timingType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('digitalProduct')
      .leftJoinAndSelect('digitalProduct.product', 'product');

    // Filter theo deliveryMethod nếu có
    if (deliveryMethod) {
      queryBuilder.andWhere('digitalProduct.deliveryMethod = :deliveryMethod', { deliveryMethod });
    }

    // Filter theo timingType nếu có
    if (timingType) {
      queryBuilder.andWhere('digitalProduct.timingType = :timingType', { timingType });
    }

    // Tìm kiếm theo tên sản phẩm nếu có
    if (search) {
      queryBuilder.andWhere('product.name ILIKE :search', { 
        search: `%${search}%` 
      });
    }

    // Sắp xếp theo ID mới nhất
    queryBuilder.orderBy('digitalProduct.id', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
