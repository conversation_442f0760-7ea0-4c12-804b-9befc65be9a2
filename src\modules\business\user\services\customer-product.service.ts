import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import {
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  CustomerProductResponseDto,
  CustomerProductListResponseDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
} from '../dto/customer-product';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { EntityStatusEnum } from '@modules/business/enums';
import { CustomerProduct } from '@modules/business/entities/customer-product.entity';

/**
 * Service xử lý logic nghiệp vụ cho sản phẩm khách hàng
 */
@Injectable()
export class CustomerProductService {
  private readonly logger = new Logger(CustomerProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
  ) {}

  /**
   * Tạo sản phẩm khách hàng mới
   * @param createDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateCustomerProductDto,
    userId: number,
  ): Promise<CustomerProductResponseDto> {
    try {
      this.logger.log(`Tạo sản phẩm khách hàng: ${createDto.name} cho userId=${userId}`);

      // Tạo entity CustomerProduct
      const productData: Partial<CustomerProduct> = {
        userId,
        name: createDto.name,
        description: createDto.description || null,
        productType: createDto.productType,
        typePrice: createDto.typePrice,
        price: createDto.price || null,
        tags: createDto.tags || [],
        status: EntityStatusEnum.PENDING, // Mặc định chờ duyệt
        customFields: this.buildCustomFields(createDto.customFields),
      };

      // Lưu vào database
      const savedProduct = await this.customerProductRepository.create(productData);

      // Chuyển đổi sang DTO response
      const responseDto = plainToInstance(CustomerProductResponseDto, savedProduct, {
        excludeExtraneousValues: true,
      });

      this.logger.log(`Tạo thành công sản phẩm khách hàng: ${savedProduct.name} (ID: ${savedProduct.id})`);
      return responseDto;

    } catch (error) {
      this.logger.error(`Lỗi khi tạo sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm khách hàng với phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại (optional, nếu có thì chỉ lấy sản phẩm của user đó)
   * @returns Danh sách sản phẩm với phân trang
   */
  async findAll(
    queryDto: QueryCustomerProductDto,
    userId?: number,
  ): Promise<PaginatedResult<CustomerProductResponseDto>> {
    try {
      // Thêm userId vào query nếu có
      const query = userId ? { ...queryDto, userId } : queryDto;

      // Lấy danh sách từ repository
      const result = await this.customerProductRepository.findAll(query);

      // Chuyển đổi items sang DTO response
      const items = result.items.map(item =>
        plainToInstance(CustomerProductResponseDto, item, {
          excludeExtraneousValues: true,
        })
      );

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại (optional, nếu có thì kiểm tra quyền sở hữu)
   * @returns Chi tiết sản phẩm
   */
  async findById(id: number, userId?: number): Promise<CustomerProductResponseDto> {
    try {
      // Tìm sản phẩm
      const product = userId 
        ? await this.customerProductRepository.findByIdAndUserId(id, userId)
        : await this.customerProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomerProductResponseDto, product, {
        excludeExtraneousValues: true,
      });

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm khách hàng
   * @param id ID của sản phẩm cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateCustomerProductDto,
    userId: number,
  ): Promise<CustomerProductResponseDto> {
    try {
      // Tìm sản phẩm và kiểm tra quyền sở hữu
      const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<CustomerProduct> = {};

      if (updateDto.name !== undefined) updateData.name = updateDto.name;
      if (updateDto.description !== undefined) updateData.description = updateDto.description;
      if (updateDto.productType !== undefined) updateData.productType = updateDto.productType;
      if (updateDto.typePrice !== undefined) updateData.typePrice = updateDto.typePrice;
      if (updateDto.price !== undefined) updateData.price = updateDto.price;
      if (updateDto.tags !== undefined) updateData.tags = updateDto.tags;
      if (updateDto.status !== undefined) updateData.status = updateDto.status;
      if (updateDto.customFields !== undefined) {
        updateData.customFields = this.buildCustomFields(updateDto.customFields);
      }

      // Cập nhật sản phẩm
      const updatedProduct = await this.customerProductRepository.update(id, updateData);

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomerProductResponseDto, updatedProduct, {
        excludeExtraneousValues: true,
      });

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi cập nhật sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sản phẩm khách hàng (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm và kiểm tra quyền sở hữu
      const product = await this.customerProductRepository.findByIdAndUserId(id, userId);

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Xóa mềm sản phẩm
      await this.customerProductRepository.softDelete(id);

      this.logger.log(`Đã xóa sản phẩm khách hàng ID: ${id}`);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi xóa sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm khách hàng cùng lúc (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Transactional()
  async bulkDelete(
    bulkDeleteDto: BulkDeleteCustomerProductDto,
    userId: number,
  ): Promise<BulkDeleteCustomerProductResponseDto> {
    try {
      const { productIds } = bulkDeleteDto;
      const results: Array<{
        productId: number;
        status: 'success' | 'error';
        message: string;
      }> = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(`Bắt đầu xóa bulk ${productIds.length} sản phẩm khách hàng cho userId=${userId}`);

      // Xử lý từng sản phẩm một để có thể báo cáo chi tiết
      for (const productId of productIds) {
        try {
          // Tìm sản phẩm và kiểm tra quyền sở hữu
          const product = await this.customerProductRepository.findByIdAndUserId(productId, userId);

          if (!product) {
            results.push({
              productId,
              status: 'error',
              message: `Không tìm thấy sản phẩm với ID ${productId}`,
            });
            failureCount++;
            continue;
          }

          // Xóa mềm sản phẩm
          await this.customerProductRepository.softDelete(productId);

          results.push({
            productId,
            status: 'success',
            message: 'Xóa sản phẩm thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(`Lỗi khi xóa sản phẩm ${productId}: ${error.message}`, error.stack);
          results.push({
            productId,
            status: 'error',
            message: `Lỗi khi xóa: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteCustomerProductResponseDto = {
        results,
        totalProcessed: productIds.length,
        successCount,
        failureCount,
        message: `Xóa thành công ${successCount}/${productIds.length} sản phẩm`,
      };

      this.logger.log(`Hoàn thành bulk delete: ${successCount}/${productIds.length} thành công`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi xóa bulk sản phẩm khách hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa bulk sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xây dựng custom fields từ DTO
   * @param customFields Danh sách custom fields từ DTO
   * @returns Custom fields object
   */
  private buildCustomFields(customFields?: any[]): any {
    if (!customFields || !customFields.length) {
      return null;
    }

    const result: any = {};
    customFields.forEach(field => {
      if (field.fieldId && field.fieldValue !== undefined) {
        result[field.fieldId] = field.fieldValue;
      }
    });

    return Object.keys(result).length > 0 ? result : null;
  }
}
